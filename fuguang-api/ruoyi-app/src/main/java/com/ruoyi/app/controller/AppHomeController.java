package com.ruoyi.app.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.ruoyi.common.annotation.Anonymous;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.fuguang.domain.AppTask;
import com.ruoyi.fuguang.domain.AppNotice;
import com.ruoyi.fuguang.domain.AppFunction;
import com.ruoyi.fuguang.domain.AppConfig;
import com.ruoyi.fuguang.service.IAppTaskService;
import com.ruoyi.fuguang.service.IAppNoticeService;
import com.ruoyi.fuguang.service.IAppConfigService;
import com.ruoyi.fuguang.service.IAppFunctionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * APP首页接口
 *
 * <AUTHOR>
 */
@Api(tags = "APP首页接口")
@RestController("appHomeApiController")
@RequestMapping("/app/home")
public class AppHomeController extends BaseController
{
    @Autowired
    private IAppTaskService appTaskService;

    @Autowired
    private IAppNoticeService appNoticeService;

    @Autowired
    private IAppFunctionService appFunctionService;

    @Autowired
    private IAppConfigService appConfigService;

    /**
     * 获取首页数据
     */
    @Anonymous
    @ApiOperation("获取首页数据")
    @GetMapping("/index")
    public AjaxResult getHomeData(
            @ApiParam("经度") @RequestParam(required = false) String longitude,
            @ApiParam("纬度") @RequestParam(required = false) String latitude)
    {
        Map<String, Object> data = new HashMap<>();
        
        // 获取首页标语
        String slogan = appConfigService.getHomeSlogan();
        data.put("slogan", slogan);
        
        // 获取最新系统通知（滚动播放）
        List<AppNotice> notices = appNoticeService.selectLatestSystemNotices(3);
        data.put("notices", notices);
        
        // 获取热门任务列表（最近十条）
        List<AppTask> hotTasks = appTaskService.selectHotTaskList(longitude, latitude, 10);
        data.put("hotTasks", hotTasks);
        
        return success(data);
    }

    /**
     * 搜索功能
     */
    @Anonymous
    @ApiOperation("搜索任务、商品、商家")
    @GetMapping("/search")
    public AjaxResult search(
            @ApiParam("搜索关键词") @RequestParam String keyword,
            @ApiParam("搜索类型") @RequestParam(defaultValue = "task") String type,
            @ApiParam("经度") @RequestParam(required = false) String longitude,
            @ApiParam("纬度") @RequestParam(required = false) String latitude)
    {
        Map<String, Object> data = new HashMap<>();
        
        if ("task".equals(type)) {
            // 搜索任务
            AppTask searchTask = new AppTask();
            searchTask.setTaskTitle(keyword);
            List<AppTask> tasks = appTaskService.selectAppTaskList(searchTask);
            data.put("tasks", tasks);
        }
        // TODO: 添加商品和商家搜索逻辑
        
        return success(data);
    }

    /**
     * 获取通知列表
     */
    @Anonymous
    @ApiOperation("获取通知列表")
    @GetMapping("/notices")
    public AjaxResult getNotices()
    {
        Long userId = getUserId();
        List<AppNotice> notices = appNoticeService.selectNoticesByUser(userId, 20);
        return success(notices);
    }

    /**
     * 标记通知为已读
     */
    @Anonymous
    @ApiOperation("标记通知为已读")
    @PostMapping("/notice/read")
    public AjaxResult markNoticeAsRead(@RequestParam Long noticeId)
    {
        Long userId = getUserId();
        int result = appNoticeService.markNoticeAsRead(noticeId, userId);
        return toAjax(result);
    }

    /**
     * 获取未读通知数量
     */

    @ApiOperation("获取未读通知数量")
    @GetMapping("/notice/unread-count")
    public AjaxResult getUnreadNoticeCount()
    {
        Long userId = getUserId();
        int count = appNoticeService.countUnreadNotices(userId);
        return success(count);
    }

    /**
     * 获取APP配置信息
     */
    @Anonymous
    @ApiOperation("获取APP配置信息")
    @GetMapping("/config")
    public AjaxResult getAppConfig()
    {
        Map<String, Object> config = new HashMap<>();
        config.put("privacyPolicy", appConfigService.getPrivacyPolicy());
        config.put("userAgreement", appConfigService.getUserAgreement());
        config.put("servicePhone", appConfigService.getServicePhone());
        return success(config);
    }

    /**
     * 二维码扫描处理
     */
    @ApiOperation("二维码扫描处理")
    @PostMapping("/qrcode/scan")
    public AjaxResult scanQrCode(@RequestBody Map<String, String> params)
    {
        String qrContent = params.get("content");
        
        // TODO: 根据二维码内容进行相应处理
        // 可能是任务二维码、商品二维码等
        
        Map<String, Object> result = new HashMap<>();
        result.put("type", "unknown");
        result.put("content", qrContent);
        
        return success(result);
    }

    /**
     * 获取兴业助农配置
     */
    @Anonymous
    @ApiOperation("获取兴业助农配置")
    @GetMapping("/agriculture")
    public AjaxResult getAgricultureConfig()
    {
        try {
            // 获取图片配置
            AppConfig appConfig = appConfigService.selectAppConfigByKey("app.agriculture.title");
            return success(appConfig);
        } catch (Exception e) {
            return error("获取兴业助农配置失败：" + e.getMessage());
        }
    }

    /**
     * 获取购物专区配置
     */
    @Anonymous
    @ApiOperation("获取购物专区配置")
    @GetMapping("/shopping")
    public AjaxResult getShoppingConfig()
    {
        try {
            // 获取图片配置
            AppConfig appConfig = appConfigService.selectAppConfigByKey("app.shopping.title");
            return success(appConfig);
        } catch (Exception e) {
            return error("获取购物专区配置失败：" + e.getMessage());
        }
    }

    /**
     * 获取更多功能配置
     */
    @Anonymous
    @ApiOperation("获取更多功能配置")
    @GetMapping("/functions")
    public AjaxResult getFunctions()
    {
        try {
            List<AppFunction> functions = appFunctionService.selectEnabledAppFunctionList();
            return success(functions);
        } catch (Exception e) {
            return error("获取功能配置失败：" + e.getMessage());
        }
    }
}
