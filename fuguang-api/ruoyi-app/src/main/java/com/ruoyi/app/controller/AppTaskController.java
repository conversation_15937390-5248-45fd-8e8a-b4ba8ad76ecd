package com.ruoyi.app.controller;

import java.util.List;

import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.page.TableDataInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.fuguang.domain.AppTask;
import com.ruoyi.fuguang.service.IAppTaskService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * APP任务接口
 *
 * <AUTHOR>
 */
@Api(tags = "APP任务接口")
@RestController("appTaskApiController")
@RequestMapping("/app/task")
public class AppTaskController extends BaseController
{
    @Autowired
    private IAppTaskService appTaskService;

    /**
     * 查询任务列表
     */
    @ApiOperation("查询任务列表")
    @GetMapping("/list")
    public TableDataInfo list(AppTask appTask)
    {
        startPage();
        List<AppTask> list = appTaskService.selectAppTaskList(appTask);
        return getDataTable(list);
    }

    /**
     * 获取任务详细信息
     */
    @ApiOperation("获取任务详细信息")
    @GetMapping(value = "/{taskId}")
    public AjaxResult getInfo(@PathVariable("taskId") Long taskId)
    {
        // 增加浏览次数
        appTaskService.increaseViewCount(taskId);
        
        AppTask task = appTaskService.selectAppTaskByTaskId(taskId);
        return success(task);
    }

    /**
     * 新增任务
     */
    @ApiOperation("发布新任务")
    @Log(title = "APP任务", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AppTask appTask)
    {
        Long userId = getUserId();
        appTask.setPublisherId(userId);
        // TODO: 设置发布者昵称和头像
        
        return toAjax(appTaskService.insertAppTask(appTask));
    }

    /**
     * 修改任务
     */
    @ApiOperation("修改任务")
    @Log(title = "APP任务", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AppTask appTask)
    {
        Long userId = getUserId();
        // 验证是否为任务发布者
        AppTask existTask = appTaskService.selectAppTaskByTaskId(appTask.getTaskId());
        if (existTask == null || !existTask.getPublisherId().equals(userId)) {
            return error("无权限修改此任务");
        }
        
        return toAjax(appTaskService.updateAppTask(appTask));
    }

    /**
     * 接取任务
     */
    @ApiOperation("接取任务")
    @Log(title = "接取任务", businessType = BusinessType.UPDATE)
    @PostMapping("/accept/{taskId}")
    public AjaxResult acceptTask(@PathVariable Long taskId)
    {
        Long userId = getUserId();
        
        // 验证任务状态
        AppTask task = appTaskService.selectAppTaskByTaskId(taskId);
        if (task == null) {
            return error("任务不存在");
        }
        if (!"0".equals(task.getTaskStatus())) {
            return error("任务状态不允许接取");
        }
        if (task.getPublisherId().equals(userId)) {
            return error("不能接取自己发布的任务");
        }
        
        int result = appTaskService.acceptTask(taskId, userId);
        return toAjax(result);
    }

    /**
     * 完成任务
     */
    @ApiOperation("完成任务")
    @Log(title = "完成任务", businessType = BusinessType.UPDATE)
    @PostMapping("/complete/{taskId}")
    public AjaxResult completeTask(@PathVariable Long taskId)
    {
        Long userId = getUserId();
        
        // 验证任务状态和权限
        AppTask task = appTaskService.selectAppTaskByTaskId(taskId);
        if (task == null) {
            return error("任务不存在");
        }
        if (!"1".equals(task.getTaskStatus())) {
            return error("任务状态不允许完成");
        }
        if (!task.getReceiverId().equals(userId)) {
            return error("只有任务接收者可以完成任务");
        }
        
        int result = appTaskService.completeTask(taskId);
        return toAjax(result);
    }

    /**
     * 取消任务
     */
    @ApiOperation("取消任务")
    @Log(title = "取消任务", businessType = BusinessType.UPDATE)
    @PostMapping("/cancel/{taskId}")
    public AjaxResult cancelTask(@PathVariable Long taskId)
    {
        Long userId = getUserId();
        
        // 验证任务状态和权限
        AppTask task = appTaskService.selectAppTaskByTaskId(taskId);
        if (task == null) {
            return error("任务不存在");
        }
        if ("2".equals(task.getTaskStatus()) || "3".equals(task.getTaskStatus())) {
            return error("任务已完成或已取消");
        }
        if (!task.getPublisherId().equals(userId)) {
            return error("只有任务发布者可以取消任务");
        }
        
        int result = appTaskService.cancelTask(taskId);
        return toAjax(result);
    }

    /**
     * 查询我发布的任务
     */
    @ApiOperation("查询我发布的任务")
    @GetMapping("/my-published")
    public AjaxResult getMyPublishedTasks()
    {
        Long userId = getUserId();
        List<AppTask> tasks = appTaskService.selectTasksByPublisher(userId);
        return success(tasks);
    }

    /**
     * 查询我接取的任务
     */
    @ApiOperation("查询我接取的任务")
    @GetMapping("/my-received")
    public AjaxResult getMyReceivedTasks()
    {
        Long userId = getUserId();
        List<AppTask> tasks = appTaskService.selectTasksByReceiver(userId);
        return success(tasks);
    }

    /**
     * 查询热门任务
     */
    @Anonymous
    @ApiOperation("查询热门任务")
    @GetMapping("/hot")
    public AjaxResult getHotTasks(
            @ApiParam("经度") @RequestParam(required = false) String longitude,
            @ApiParam("纬度") @RequestParam(required = false) String latitude,
            @ApiParam("限制数量") @RequestParam(defaultValue = "10") Integer limit)
    {
        List<AppTask> tasks = appTaskService.selectHotTaskList(longitude, latitude, limit);
        return success(tasks);
    }
}
