<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.fuguang.mapper.AppFunctionMapper">
    
    <resultMap type="AppFunction" id="AppFunctionResult">
        <result property="functionId"    column="function_id"    />
        <result property="functionName"    column="function_name"    />
        <result property="functionIcon"    column="function_icon"    />
        <result property="functionUrl"    column="function_url"    />
        <result property="functionType"    column="function_type"    />
        <result property="sortOrder"    column="sort_order"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectAppFunctionVo">
        select function_id, function_name, function_icon, function_url, function_type, sort_order, status, create_by, create_time, update_by, update_time, remark from app_function
    </sql>

    <select id="selectAppFunctionList" parameterType="AppFunction" resultMap="AppFunctionResult">
        <include refid="selectAppFunctionVo"/>
        <where>  
            <if test="functionName != null  and functionName != ''"> and function_name like concat('%', #{functionName}, '%')</if>
            <if test="functionIcon != null  and functionIcon != ''"> and function_icon = #{functionIcon}</if>
            <if test="functionUrl != null  and functionUrl != ''"> and function_url = #{functionUrl}</if>
            <if test="functionType != null  and functionType != ''"> and function_type = #{functionType}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
        order by sort_order asc
    </select>
    
    <select id="selectAppFunctionByFunctionId" parameterType="Long" resultMap="AppFunctionResult">
        <include refid="selectAppFunctionVo"/>
        where function_id = #{functionId}
    </select>

    <select id="selectEnabledAppFunctionList" resultMap="AppFunctionResult">
        <include refid="selectAppFunctionVo"/>
        where status = '0'
        order by sort_order asc
    </select>
        
    <insert id="insertAppFunction" parameterType="AppFunction" useGeneratedKeys="true" keyProperty="functionId">
        insert into app_function
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="functionName != null and functionName != ''">function_name,</if>
            <if test="functionIcon != null">function_icon,</if>
            <if test="functionUrl != null">function_url,</if>
            <if test="functionType != null">function_type,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="functionName != null and functionName != ''">#{functionName},</if>
            <if test="functionIcon != null">#{functionIcon},</if>
            <if test="functionUrl != null">#{functionUrl},</if>
            <if test="functionType != null">#{functionType},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateAppFunction" parameterType="AppFunction">
        update app_function
        <trim prefix="SET" suffixOverrides=",">
            <if test="functionName != null and functionName != ''">function_name = #{functionName},</if>
            <if test="functionIcon != null">function_icon = #{functionIcon},</if>
            <if test="functionUrl != null">function_url = #{functionUrl},</if>
            <if test="functionType != null">function_type = #{functionType},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where function_id = #{functionId}
    </update>

    <delete id="deleteAppFunctionByFunctionId" parameterType="Long">
        delete from app_function where function_id = #{functionId}
    </delete>

    <delete id="deleteAppFunctionByFunctionIds" parameterType="String">
        delete from app_function where function_id in 
        <foreach item="functionId" collection="array" open="(" separator="," close=")">
            #{functionId}
        </foreach>
    </delete>
</mapper>
